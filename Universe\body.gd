extends Area2D

@export var mass: float = 1e16 * Universe.MASS_SCALE  # Default asteroid mass
@export var velocity: Vector2 = Vector2.ZERO
@export var density: float = Universe.DENSITY_ROCK  # Material density
@export var body_type: String = "asteroid"  # For classification

# Preload resources for performance
const BODY_SCENE = preload("res://Universe/body.tscn")
const TEX_ASTEROID = preload("res://assets/asteroid.png")
const TEX_MOON     = preload("res://assets/asteroid.png")
const TEX_PLANET   = preload("res://assets/asteroid.png")
const TEX_STAR     = preload("res://assets/asteroid.png")
const TEX_BLACKHOLE = preload("res://assets/asteroid.png")

@onready var collision_shape: CollisionShape2D = $CollisionShape2D
@onready var sprite: Sprite2D = $Sprite2D
var circle_shape: CircleShape2D

var force: Vector2 = Vector2.ZERO
var radius: float
var volume: float  # Physical volume in m³
var schwarzschild_radius: float  # For black hole physics

func _ready():
	circle_shape = collision_shape.shape as CircleShape2D
	Universe.bodies.append(self)
	classify_body()
	update_physics_properties()
	update_visual()

func _physics_process(delta: float) -> void:
	force = Vector2.ZERO
	apply_gravity()
	apply_tidal_forces()
	move_self(delta)
	handle_collisions()
	check_stability()

func apply_force(f: Vector2) -> void:
	force += f

func apply_gravity() -> void:
	# Calculate gravitational pull from all valid bodies using Newton's law of universal gravitation
	# F = G * m1 * m2 / r²
	for other in Universe.bodies:
		if other == self or not is_instance_valid(other):
			continue

		var dir = other.global_position - global_position
		var distance = max(dir.length(), Universe.MIN_DIST)
		var dist_sq = distance * distance

		# Newton's law of universal gravitation
		var force_mag = Universe.G * mass * other.mass / dist_sq

		# Apply relativistic correction for very massive objects (black holes)
		if other.body_type == "black_hole" or body_type == "black_hole":
			force_mag *= get_relativistic_correction(distance, other)

		apply_force(dir.normalized() * force_mag)

func move_self(delta: float) -> void:
	var acc = force / mass
	velocity += acc * delta
	global_position += velocity * delta

func handle_collisions() -> void:
	# Check collisions against all valid bodies
	for other in Universe.bodies:
		if other == self or not is_instance_valid(other):
			continue
		var dist = global_position.distance_to(other.global_position)
		if dist < radius + other.radius:
			Universe.bodies.erase(self)
			Universe.bodies.erase(other)

			# Calculate collision physics based on kinetic energy and escape velocity
			var relative_velocity = (velocity - other.velocity).length()
			var total_mass = mass + other.mass
			var kinetic_energy = 0.5 * (mass * other.mass / total_mass) * relative_velocity * relative_velocity
			var escape_velocity = get_escape_velocity(total_mass, radius + other.radius)

			# Determine collision outcome based on physics
			if relative_velocity < escape_velocity * 0.5:
				# Low energy collision - merge (inelastic collision)
				call_deferred("_perform_merge", other)
			else:
				# High energy collision - fragmentation
				call_deferred("_perform_fragment", other, kinetic_energy)
			return

func _perform_merge(other: Area2D) -> void:
	if not is_instance_valid(other):
		return

	# Conservation of momentum: p = mv
	var total_mass = mass + other.mass
	var momentum_self = velocity * mass
	var momentum_other = other.velocity * other.mass
	velocity = (momentum_self + momentum_other) / total_mass

	# Conservation of mass
	mass = total_mass

	# Weighted average of densities based on volume
	var volume_self = (4.0/3.0) * PI * pow(radius, 3)
	var volume_other = (4.0/3.0) * PI * pow(other.radius, 3)
	var total_volume = volume_self + volume_other
	density = (density * volume_self + other.density * volume_other) / total_volume

	classify_body()
	update_physics_properties()
	update_visual()
	other.queue_free()

func _perform_fragment(other: Area2D, kinetic_energy: float) -> void:
	if not is_instance_valid(other):
		return

	var total_mass = mass + other.mass
	var center_of_mass_velocity = (velocity * mass + other.velocity * other.mass) / total_mass

	# Calculate number of fragments based on impact energy
	# More energetic collisions create more fragments
	var fragment_count = int(clamp(kinetic_energy / (total_mass * 100), 2, 8))

	# Mass distribution follows power law (realistic fragmentation)
	var fragments_mass = []
	var remaining_mass = total_mass

	for i in range(fragment_count - 1):
		var frag_mass = remaining_mass * randf_range(0.1, 0.4)
		fragments_mass.append(frag_mass)
		remaining_mass -= frag_mass
	fragments_mass.append(remaining_mass)  # Last fragment gets remaining mass

	# Create fragments with conservation of momentum
	var total_momentum = Vector2.ZERO
	for i in range(fragment_count):
		var frag = BODY_SCENE.instantiate()
		frag.mass = fragments_mass[i]
		frag.density = (density + other.density) / 2.0  # Average density

		# Position fragments around collision point
		var angle = (2.0 * PI * i) / fragment_count
		frag.global_position = global_position + Vector2(cos(angle), sin(angle)) * radius * 0.5

		# Velocity based on conservation of momentum and energy
		var escape_vel = get_escape_velocity(frag.mass, frag.radius)
		var frag_speed = randf_range(escape_vel * 0.3, escape_vel * 1.2)
		frag.velocity = center_of_mass_velocity + Vector2(cos(angle), sin(angle)) * frag_speed

		total_momentum += frag.velocity * frag.mass

		get_parent().add_child(frag)
		Universe.bodies.append(frag)

	queue_free()
	other.queue_free()

func classify_body() -> void:
	# Classify body type based on realistic mass thresholds
	if mass >= Universe.MASS_BLACK_HOLE_MIN:
		body_type = "black_hole"
		density = Universe.DENSITY_NEUTRON_STAR  # Extremely dense
	elif mass >= Universe.MASS_STAR_MAX * 0.08:  # Brown dwarf limit
		body_type = "star"
		density = Universe.DENSITY_STAR
	elif mass >= Universe.MASS_PLANET_MAX * 0.1:  # Large planet
		body_type = "planet"
		# Gas giant vs rocky planet based on mass
		if mass >= Universe.MASS_PLANET_MAX * 0.3:
			density = Universe.DENSITY_GAS_GIANT
		else:
			density = Universe.DENSITY_ROCK
	elif mass >= Universe.MASS_MOON_MAX * 0.1:
		body_type = "moon"
		density = Universe.DENSITY_ROCK
	else:
		body_type = "asteroid"
		# Vary asteroid composition
		var rand = randf()
		if rand < 0.7:
			density = Universe.DENSITY_ROCK
		elif rand < 0.9:
			density = Universe.DENSITY_ICE
		else:
			density = Universe.DENSITY_IRON

func update_physics_properties() -> void:
	# Calculate realistic radius from mass and density
	# Volume = mass / density, radius = (3V/4π)^(1/3)
	volume = mass / density
	var real_radius = pow((3.0 * volume) / (4.0 * PI), 1.0/3.0)

	# Scale radius for visualization (real celestial bodies would be invisible at scale)
	radius = clamp(real_radius * Universe.DISTANCE_SCALE * 1e6, 2.0, 100.0)

	# Calculate Schwarzschild radius for black holes
	if body_type == "black_hole":
		schwarzschild_radius = (2.0 * Universe.G * mass) / (Universe.SPEED_OF_LIGHT * Universe.SPEED_OF_LIGHT)
		# For black holes, use event horizon as visual radius
		radius = max(radius, schwarzschild_radius * Universe.DISTANCE_SCALE * 1e9)

	(collision_shape.shape as CircleShape2D).radius = radius

func update_visual() -> void:
	# Set texture based on realistic body classification
	var tex = TEX_ASTEROID
	match body_type:
		"black_hole":
			tex = TEX_BLACKHOLE
		"star":
			tex = TEX_STAR
		"planet":
			tex = TEX_PLANET
		"moon":
			tex = TEX_MOON
		"asteroid":
			tex = TEX_ASTEROID

	sprite.texture = tex
	sprite.scale = Vector2.ONE * radius / 32.0

# Physics helper functions
func get_escape_velocity(object_mass: float, distance: float) -> float:
	# v_escape = sqrt(2GM/r)
	return sqrt(2.0 * Universe.G * object_mass / distance)

func get_orbital_velocity(central_mass: float, distance: float) -> float:
	# v_orbital = sqrt(GM/r)
	return sqrt(Universe.G * central_mass / distance)

func get_relativistic_correction(distance: float, other_body) -> float:
	# Simple relativistic correction for very massive objects
	if other_body.body_type == "black_hole":
		var rs = other_body.schwarzschild_radius
		if distance < rs * 3.0:  # Within 3 Schwarzschild radii
			return 1.0 + (rs / distance)  # Simplified correction
	return 1.0

func get_roche_limit(primary_mass: float, secondary_mass: float, secondary_radius: float) -> float:
	# Roche limit = 2.44 * R_primary * (ρ_primary/ρ_secondary)^(1/3)
	var primary_density = mass / volume if volume > 0 else density
	var secondary_density = secondary_mass / ((4.0/3.0) * PI * pow(secondary_radius, 3))
	return 2.44 * radius * pow(primary_density / secondary_density, 1.0/3.0)

func apply_tidal_forces() -> void:
	# Apply tidal forces for bodies within Roche limit of massive objects
	for other in Universe.bodies:
		if other == self or not is_instance_valid(other):
			continue

		# Only apply tidal forces if one body is significantly more massive
		if other.mass > mass * 10.0:
			var distance = global_position.distance_to(other.global_position)
			var roche_limit = other.get_roche_limit(other.mass, mass, radius)

			if distance < roche_limit:
				# Body is within Roche limit - apply tidal disruption
				var tidal_force_magnitude = 2.0 * Universe.G * other.mass * mass * radius / pow(distance, 3)
				var direction_to_primary = (other.global_position - global_position).normalized()

				# Tidal force tries to stretch the body
				apply_force(direction_to_primary * tidal_force_magnitude * 0.1)

func check_stability() -> void:
	# Check for various stability conditions

	# 1. Check if velocity exceeds speed of light (relativistic limit)
	var speed = velocity.length()
	if speed > Universe.SPEED_OF_LIGHT * Universe.TIME_SCALE:
		velocity = velocity.normalized() * Universe.SPEED_OF_LIGHT * Universe.TIME_SCALE * 0.99

	# 2. Check for numerical instabilities (very high accelerations)
	var acceleration = force / mass
	if acceleration.length() > 1e6:
		force = force.normalized() * mass * 1e6

	# 3. Energy conservation check (optional - for debugging)
	if randf() < 0.001:  # Check occasionally to avoid performance hit
		calculate_system_energy()

func calculate_system_energy() -> float:
	# Calculate total energy of the system for conservation checks
	var kinetic_energy = 0.5 * mass * velocity.length_squared()
	var potential_energy = 0.0

	for other in Universe.bodies:
		if other == self or not is_instance_valid(other):
			continue
		var distance = max(global_position.distance_to(other.global_position), Universe.MIN_DIST)
		potential_energy -= Universe.G * mass * other.mass / distance

	return kinetic_energy + potential_energy * 0.5  # Avoid double counting

func get_hill_sphere_radius(primary_mass: float, distance_to_primary: float) -> float:
	# Hill sphere radius = a * (m/3M)^(1/3) where a is orbital distance
	return distance_to_primary * pow(mass / (3.0 * primary_mass), 1.0/3.0)
