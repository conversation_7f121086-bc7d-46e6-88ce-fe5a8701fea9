extends Node

# Test script to verify realistic physics calculations
func _ready():
	print("=== PHYSICS VALIDATION TESTS ===")
	test_gravitational_force()
	test_escape_velocity()
	test_orbital_velocity()
	test_mass_radius_relationship()
	test_body_classification()
	print("=== ALL TESTS COMPLETED ===")

func test_gravitational_force():
	print("\n1. Testing Gravitational Force (Newton's Law)")
	
	# Test with Earth-Moon system (scaled)
	var earth_mass = Universe.EARTH_MASS * Universe.MASS_SCALE
	var moon_mass = Universe.MOON_MASS * Universe.MASS_SCALE
	var distance = 384400.0 * Universe.DISTANCE_SCALE  # Earth-Moon distance
	
	var force = Universe.G * earth_mass * moon_mass / (distance * distance)
	print("Earth-Moon gravitational force: ", force, " N (scaled)")
	
	# Verify force is reasonable (should be positive)
	assert(force > 0, "Gravitational force should be positive")
	print("✓ Gravitational force calculation correct")

func test_escape_velocity():
	print("\n2. Testing Escape Velocity")
	
	# Test with Earth values
	var earth_mass = Universe.EARTH_MASS * Universe.MASS_SCALE
	var earth_radius = 6371000.0 * Universe.DISTANCE_SCALE  # Earth radius in meters
	
	var escape_vel = sqrt(2.0 * Universe.G * earth_mass / earth_radius)
	print("Earth escape velocity: ", escape_vel, " m/s (scaled)")
	
	# Real Earth escape velocity is ~11,200 m/s
	# Our scaled version should be proportional
	assert(escape_vel > 0, "Escape velocity should be positive")
	print("✓ Escape velocity calculation correct")

func test_orbital_velocity():
	print("\n3. Testing Orbital Velocity")
	
	var earth_mass = Universe.EARTH_MASS * Universe.MASS_SCALE
	var orbit_radius = 400000.0 * Universe.DISTANCE_SCALE  # ISS orbit ~400km altitude
	
	var orbital_vel = sqrt(Universe.G * earth_mass / orbit_radius)
	print("Low Earth orbit velocity: ", orbital_vel, " m/s (scaled)")
	
	assert(orbital_vel > 0, "Orbital velocity should be positive")
	print("✓ Orbital velocity calculation correct")

func test_mass_radius_relationship():
	print("\n4. Testing Mass-Radius Relationship")
	
	# Test with different densities
	var test_mass = 1e20  # kg
	var rock_density = Universe.DENSITY_ROCK
	
	var volume = test_mass / rock_density
	var radius = pow((3.0 * volume) / (4.0 * PI), 1.0/3.0)
	
	print("Test object (", test_mass, " kg, rock density):")
	print("  Volume: ", volume, " m³")
	print("  Radius: ", radius, " m")
	
	# Verify volume and radius are reasonable
	assert(volume > 0, "Volume should be positive")
	assert(radius > 0, "Radius should be positive")
	
	# Test that higher density gives smaller radius for same mass
	var iron_density = Universe.DENSITY_IRON
	var iron_volume = test_mass / iron_density
	var iron_radius = pow((3.0 * iron_volume) / (4.0 * PI), 1.0/3.0)
	
	assert(iron_radius < radius, "Iron object should be smaller than rock object of same mass")
	print("✓ Mass-radius relationship correct")

func test_body_classification():
	print("\n5. Testing Body Classification")
	
	# Test mass thresholds
	print("Mass thresholds:")
	print("  Asteroid max: ", Universe.MASS_ASTEROID_MAX)
	print("  Moon max: ", Universe.MASS_MOON_MAX)
	print("  Planet max: ", Universe.MASS_PLANET_MAX)
	print("  Star max: ", Universe.MASS_STAR_MAX)
	print("  Black hole min: ", Universe.MASS_BLACK_HOLE_MIN)
	
	# Verify thresholds are in ascending order
	assert(Universe.MASS_ASTEROID_MAX < Universe.MASS_MOON_MAX, "Asteroid < Moon mass")
	assert(Universe.MASS_MOON_MAX < Universe.MASS_PLANET_MAX, "Moon < Planet mass")
	assert(Universe.MASS_PLANET_MAX < Universe.MASS_STAR_MAX, "Planet < Star mass")
	assert(Universe.MASS_STAR_MAX < Universe.MASS_BLACK_HOLE_MIN, "Star < Black hole mass")
	
	print("✓ Body classification thresholds correct")

func test_conservation_laws():
	print("\n6. Testing Conservation Laws")
	
	# Test momentum conservation in collision
	var mass1 = 1000.0
	var mass2 = 2000.0
	var vel1 = Vector2(10, 0)
	var vel2 = Vector2(-5, 0)
	
	var momentum_before = mass1 * vel1 + mass2 * vel2
	var total_mass = mass1 + mass2
	var vel_after = momentum_before / total_mass
	
	var momentum_after = total_mass * vel_after
	
	print("Momentum before: ", momentum_before)
	print("Momentum after: ", momentum_after)
	print("Difference: ", momentum_before - momentum_after)
	
	# Should be very close to zero (within floating point precision)
	assert(abs((momentum_before - momentum_after).length()) < 0.001, "Momentum should be conserved")
	print("✓ Momentum conservation correct")
