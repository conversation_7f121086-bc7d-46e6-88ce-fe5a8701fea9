extends Node

var bodies: Array[Area2D] = []

# Real physics constants (scaled for simulation)
# Real G = 6.674e-11 m³/kg⋅s², scaled for pixel units and reasonable simulation speed
const G = 6.674e-4  # Gravitational constant (scaled)
const MIN_DIST = 1.0  # Minimum distance to prevent singularities (pixels)

# Physical constants for realistic simulation
const SPEED_OF_LIGHT = 299792458.0  # m/s (for relativistic effects)
const SOLAR_MASS = 1.989e30  # kg
const EARTH_MASS = 5.972e24  # kg
const MOON_MASS = 7.342e22   # kg

# Simulation scaling factors
const MASS_SCALE = 1e-20     # Scale factor to convert real masses to simulation units
const DISTANCE_SCALE = 1e-6  # Scale factor for distances (1 pixel = 1000 km)
const TIME_SCALE = 1e-3      # Time acceleration factor

# Material densities (kg/m³)
const DENSITY_ROCK = 2700.0      # Rocky asteroids/terrestrial planets
const DENSITY_ICE = 917.0        # Icy bodies
const DENSITY_IRON = 7874.0      # Metallic asteroids/cores
const DENSITY_GAS_GIANT = 1326.0 # Gas giants (Jupiter-like)
const DENSITY_STAR = 1408.0      # Main sequence stars (Sun-like)
const DENSITY_WHITE_DWARF = 1e9  # White dwarf stars
const DENSITY_NEUTRON_STAR = 4e17 # Neutron stars

# Body type mass thresholds (in simulation units, scaled from real values)
const MASS_ASTEROID_MAX = 1e16 * MASS_SCALE    # ~Ceres mass
const MASS_MOON_MAX = 1e23 * MASS_SCALE        # ~Large moon mass
const MASS_PLANET_MAX = 1e28 * MASS_SCALE      # ~Jupiter mass
const MASS_STAR_MAX = 1e32 * MASS_SCALE        # ~100 solar masses
const MASS_BLACK_HOLE_MIN = 3 * SOLAR_MASS * MASS_SCALE # Chandrasekhar limit

# Additional physics constants
const BOLTZMANN_CONSTANT = 1.381e-23  # J/K
const STEFAN_BOLTZMANN = 5.67e-8      # W⋅m⁻²⋅K⁻⁴
const PLANCK_CONSTANT = 6.626e-34     # J⋅s

# Simulation performance settings
const MAX_BODIES = 1000               # Maximum number of bodies
const COLLISION_TREE_DEPTH = 8       # For spatial optimization
const ENERGY_CONSERVATION_TOLERANCE = 0.01  # Energy drift tolerance
