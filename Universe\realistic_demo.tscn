[gd_scene load_steps=2 format=3 uid="uid://xtlkdjik5lu5"]

[ext_resource type="PackedScene" uid="uid://dl0ucdd447qe8" path="res://Universe/body.tscn" id="1_prsg5"]

[node name="RealisticDemo" type="Node2D"]

[node name="CentralStar" parent="." instance=ExtResource("1_prsg5")]
position = Vector2(640, 360)
mass = 1.989e+10
density = 1408.0
body_type = "star"

[node name="Planet1" parent="." instance=ExtResource("1_prsg5")]
position = Vector2(840, 360)
mass = 59720.0
velocity = Vector2(0, -50)
density = 5515.0
body_type = "planet"

[node name="Planet2" parent="." instance=ExtResource("1_prsg5")]
position = Vector2(440, 360)
mass = 6390.0
velocity = Vector2(0, 60)
density = 3933.0
body_type = "planet"

[node name="Moon1" parent="." instance=ExtResource("1_prsg5")]
position = Vector2(870, 360)
mass = 734.2
velocity = Vector2(0, -80)
density = 3344.0
body_type = "moon"

[node name="Asteroid1" parent="." instance=ExtResource("1_prsg5")]
position = Vector2(1000, 360)
mass = 0.939
velocity = Vector2(0, -30)

[node name="Asteroid2" parent="." instance=ExtResource("1_prsg5")]
position = Vector2(280, 360)
mass = 1.2
velocity = Vector2(0, 40)
density = 917.0

[node name="Asteroid3" parent="." instance=ExtResource("1_prsg5")]
position = Vector2(640, 200)
mass = 0.8
velocity = Vector2(35, 0)
density = 7874.0

[node name="Asteroid4" parent="." instance=ExtResource("1_prsg5")]
position = Vector2(640, 520)
mass = 1.5
velocity = Vector2(-25, 0)
